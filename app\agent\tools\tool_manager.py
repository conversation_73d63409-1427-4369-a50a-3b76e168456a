"""
Tool Manager for unified tool handling.

This module provides a unified interface for managing tools by automatically
discovering and registering all tools from the tools directory.
"""

import importlib
import logging
import sys
from typing import Dict, Any, List, Set, Tuple, Optional
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed

from app.agent.tools.base import BaseTool, tool_registry, ToolResult

logger = logging.getLogger(__name__)


class ToolManager:
    """Unified tool manager that handles tool discovery and management."""

    def __init__(self):
        self.tool_registry = tool_registry
        self._tools_loaded = False
        self._loaded_modules: Set[str] = set()
        self._failed_modules: Set[str] = set()
        self._discovery_cache: Dict[str, List[str]] = {}

    def discover_and_load_tools(self, force_reload: bool = False) -> Dict[str, Any]:
        """Discover and load all tools from the tools directory.
        
        Args:
            force_reload: Whether to force reload already loaded modules
            
        Returns:
            Dict[str, Any]: Discovery results summary
        """
        if self._tools_loaded and not force_reload:
            return self._get_discovery_summary()

        tools_dir = Path(__file__).parent
        logger.info(f"Discovering tools in: {tools_dir}")

        # Find all Python files in the tools directory
        tool_files = [
            file_path for file_path in tools_dir.glob("*.py")
            if not file_path.name.startswith("__") and file_path.name not in ["tool_manager.py", "base.py"]
        ]

        # Load modules with parallel processing for better performance
        loaded_count = 0
        failed_count = 0
        
        with ThreadPoolExecutor(max_workers=4) as executor:
            future_to_module = {
                executor.submit(self._load_module, file_path): file_path
                for file_path in tool_files
            }
            
            for future in as_completed(future_to_module):
                file_path = future_to_module[future]
                try:
                    success = future.result()
                    if success:
                        loaded_count += 1
                    else:
                        failed_count += 1
                except Exception as e:
                    logger.error(f"Unexpected error loading {file_path}: {str(e)}")
                    failed_count += 1

        self._tools_loaded = True
        
        summary = {
            'total_files': len(tool_files),
            'loaded_count': loaded_count,
            'failed_count': failed_count,
            'registered_tools': self.get_available_tool_names(),
            'failed_modules': list(self._failed_modules)
        }
        
        logger.info(f"Tool discovery complete. {summary}")
        return summary
    
    def _load_module(self, file_path: Path) -> bool:
        """Load a single module file.
        
        Args:
            file_path: Path to the module file
            
        Returns:
            bool: True if loaded successfully
        """
        module_name = f"app.agent.tools.{file_path.stem}"
        
        try:
            if module_name in self._loaded_modules:
                # Reload if already loaded
                if module_name in sys.modules:
                    importlib.reload(sys.modules[module_name])
                else:
                    importlib.import_module(module_name)
            else:
                importlib.import_module(module_name)
            
            self._loaded_modules.add(module_name)
            self._failed_modules.discard(module_name)
            logger.debug(f"Successfully loaded tool module: {module_name}")
            return True
            
        except Exception as e:
            self._failed_modules.add(module_name)
            logger.error(f"Failed to load tool module {module_name}: {str(e)}")
            return False
    
    def _get_discovery_summary(self) -> Dict[str, Any]:
        """Get current discovery summary.
        
        Returns:
            Dict[str, Any]: Discovery summary
        """
        return {
            'loaded_modules': len(self._loaded_modules),
            'failed_modules': len(self._failed_modules),
            'registered_tools': self.get_available_tool_names(),
            'total_tools': len(self.get_available_tool_names())
        }

    def get_available_tool_names(self) -> List[str]:
        """Get all available tool names."""
        return self.tool_registry.list_tools()
    
    def get_function_schemas_for_role(self, tool_names: List[str]) -> Tuple[List[Dict[str, Any]], List[str]]:
        """
        Get OpenAI function schemas for a specific role.
        
        This method specifically prepares tools for LLM function calling by converting
        them to OpenAI's function calling format.

        Args:
            tool_names: List of tool names required by the role

        Returns:
            Tuple[List[Dict[str, Any]], List[str]]: (openai_function_schemas, missing_tools)
        """
        self.discover_and_load_tools()

        # Use the registry to get tools and missing tools
        found_tools, missing_tools = self.tool_registry.get_tools_by_names(tool_names)
        
        # Convert tools to OpenAI function schemas
        schemas = []
        conversion_failures = []
        
        for tool in found_tools:
            try:
                schema = self._convert_tool_to_function_schema(tool)
                schemas.append(schema)
            except Exception as e:
                logger.error(f"Failed to convert tool {tool.name} to schema: {str(e)}")
                conversion_failures.append(tool.name)

        # Add conversion failures to missing tools
        all_missing = missing_tools + conversion_failures

        if all_missing:
            logger.warning(f"Missing/failed tools for role: {all_missing}")

        logger.info(f"Loaded {len(schemas)} function schemas for role: {[s['function']['name'] for s in schemas]}")
        return schemas, all_missing

    def _convert_tool_to_function_schema(self, tool: BaseTool) -> Dict[str, Any]:
        """Convert a BaseTool to OpenAI function schema format.
        
        Args:
            tool: BaseTool instance
            
        Returns:
            Dict[str, Any]: OpenAI function schema
            
        Raises:
            ValueError: If tool schema is invalid
        """
        try:
            tool_schema = tool.get_schema()
            
            if not tool_schema.name:
                raise ValueError(f"Tool {type(tool).__name__} has no name")
            
            if not tool_schema.description:
                raise ValueError(f"Tool {tool_schema.name} has no description")
            
            return {
                "type": "function",
                "function": {
                    "name": tool_schema.name,
                    "description": tool_schema.description,
                    "parameters": tool_schema.parameters or {}
                }
            }
        except Exception as e:
            logger.error(f"Failed to convert tool {getattr(tool, 'name', 'unknown')} to schema: {str(e)}")
            raise
    
    async def execute_tool(self, tool_name: str, arguments: Dict[str, Any], user_context: Optional[Dict[str, Any]] = None) -> ToolResult:
        """
        Execute a tool by name with given arguments and user context.

        Args:
            tool_name: Name of the tool to execute
            arguments: Tool arguments
            user_context: Optional user context containing user_id, tenant_id, session_key

        Returns:
            ToolResult: Execution result
        """
        # Handle duplicated tool names (e.g., "user_database_lookupuser_database_lookup")
        # This is a defensive fix for streaming tool call accumulation issues
        available_tools = self.get_available_tool_names()
        original_tool_name = tool_name
        
        # Check if tool name is duplicated by checking if any available tool appears twice
        for available_tool in available_tools:
            if available_tool in tool_name and tool_name.count(available_tool) > 1:
                # Found a duplicated tool name, use the correct one
                tool_name = available_tool
                logger.warning(f"Detected duplicated tool name '{original_tool_name}', using '{tool_name}'")
                break
        
        tool = self.tool_registry.get_tool(tool_name)
        if not tool:
            return ToolResult.error_result(
                f"Tool '{original_tool_name}' not found",
                {'available_tools': self.get_available_tool_names()}
            )
        
        try:
            # Validate parameters before execution
            is_valid, error_msg = tool.validate_parameters(arguments)
            if not is_valid:
                return ToolResult.error_result(
                    f"Parameter validation failed: {error_msg}",
                    {'tool_name': original_tool_name, 'provided_args': list(arguments.keys())}
                )
            
            # Execute the tool with user context
            result = await tool.execute(user_context=user_context, **arguments)
            
            # Add execution metadata
            result.metadata.update({
                'tool_name': original_tool_name,
                'execution_time': result.metadata.get('execution_time', 'unknown')
            })
            
            return result
            
        except Exception as e:
            logger.error(f"Error executing tool {original_tool_name}: {str(e)}")
            return ToolResult.error_result(
                f"Tool execution failed: {str(e)}",
                {'tool_name': original_tool_name, 'exception_type': type(e).__name__}
            )
    
    def validate_role_tools(self, tool_names: List[str]) -> Dict[str, Any]:
        """
        Validate that all tools required by a role are available.

        Args:
            tool_names: List of tool names to validate

        Returns:
            Validation result with available and missing tools
        """
        self.discover_and_load_tools()

        available_tools = self.get_available_tool_names()
        missing_tools = [name for name in tool_names if name not in available_tools]
        valid_tools = [name for name in tool_names if name in available_tools]
        
        # Additional validation: check if valid tools can generate schemas
        invalid_tools = []
        for tool_name in valid_tools:
            tool = self.tool_registry.get_tool(tool_name)
            if tool:
                try:
                    tool.get_schema()
                except Exception as e:
                    logger.error(f"Tool {tool_name} schema validation failed: {str(e)}")
                    invalid_tools.append(tool_name)
                    valid_tools.remove(tool_name)

        return {
            "valid": len(missing_tools) == 0 and len(invalid_tools) == 0,
            "available_tools": valid_tools,
            "missing_tools": missing_tools,
            "invalid_tools": invalid_tools,
            "total_requested": len(tool_names),
            "total_available": len(valid_tools),
            "validation_summary": {
                "all_found": len(missing_tools) == 0,
                "all_valid": len(invalid_tools) == 0,
                "success_rate": len(valid_tools) / len(tool_names) if tool_names else 0
            }
        }
    
    def get_tool_health(self) -> Dict[str, Any]:
        """
        Get health status of all registered tools.
        
        Returns:
            Dict[str, Any]: Tool health report
        """
        self.discover_and_load_tools()
        
        health_report = {
            'total_tools': len(self.get_available_tool_names()),
            'healthy_tools': [],
            'unhealthy_tools': [],
            'tool_details': {}
        }
        
        for tool_name in self.get_available_tool_names():
            tool = self.tool_registry.get_tool(tool_name)
            if tool:
                try:
                    schema = tool.get_schema()
                    health_report['healthy_tools'].append(tool_name)
                    health_report['tool_details'][tool_name] = {
                        'status': 'healthy',
                        'has_schema': True,
                        'schema_name': schema.name,
                        'description': schema.description[:100] + '...' if len(schema.description) > 100 else schema.description
                    }
                except Exception as e:
                    health_report['unhealthy_tools'].append(tool_name)
                    health_report['tool_details'][tool_name] = {
                        'status': 'unhealthy',
                        'error': str(e),
                        'has_schema': False
                    }
        
        return health_report
    
    def reload_tools(self) -> Dict[str, Any]:
        """
        Reload all tools from the tools directory.
        
        Returns:
            Dict[str, Any]: Reload summary
        """
        logger.info("Reloading all tools")
        self._tools_loaded = False
        self._loaded_modules.clear()
        self._failed_modules.clear()
        
        # Clear registry
        for tool_name in list(self.tool_registry.list_tools()):
            self.tool_registry.unregister_tool(tool_name)
        
        return self.discover_and_load_tools(force_reload=True)


# Global tool manager instance
tool_manager = ToolManager()

# Auto-discover tools on module import
try:
    tool_manager.discover_and_load_tools()
except Exception as e:
    logger.error(f"Failed to auto-discover tools on import: {str(e)}")
