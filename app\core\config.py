from functools import lru_cache
from pydantic import Field
from pydantic_settings import BaseSettings
from typing import Optional
import os


class Settings(BaseSettings):
    # Application
    app_name: str = Field(default="Xapa AI", env="APP_NAME")
    app_env: str = Field(default="development", env="APP_ENV")
    debug: bool = Field(default=True, env="DEBUG")
    
    # Database
    database_url: str = Field(env="DATABASE_URL")
    redis_url: str = Field(env="REDIS_URL")
    
    # LLM Provider Configuration
    llm_provider: str = Field(default="azure_openai", env="LLM_PROVIDER")
    
    # OpenAI
    openai_api_key: Optional[str] = Field(default=None, env="OPENAI_API_KEY")
    openai_model: str = Field(default="gpt-4", env="OPENAI_MODEL")

    # Azure OpenAI
    azure_openai_api_key: Optional[str] = Field(default=None, env="AZURE_OPENAI_API_KEY")
    azure_openai_endpoint: Optional[str] = Field(default=None, env="AZURE_OPENAI_ENDPOINT")
    azure_openai_api_version: str = Field(default="2024-02-15-preview", env="AZURE_OPENAI_API_VERSION")
    azure_openai_model: Optional[str] = Field(default=None, env="AZURE_OPENAI_MODEL")

    # Embedding Provider Configuration
    embedding_provider: str = Field(default="azure_openai", env="EMBEDDING_PROVIDER")

    # OpenAI Embeddings
    openai_embedding_model: str = Field(default="text-embedding-3-small", env="OPENAI_EMBEDDING_MODEL")

    # Azure OpenAI Embeddings
    azure_openai_embedding_model: str = Field(default="text-embedding-3-small", env="AZURE_OPENAI_EMBEDDING_MODEL")

    # Pinecone
    pinecone_api_key: Optional[str] = Field(default=None, env="PINECONE_API_KEY")
    pinecone_index_name: Optional[str] = Field(default=None, env="PINECONE_INDEX_NAME")
    
    # Security
    secret_key: str = Field(env="SECRET_KEY")
    jwt_algorithm: str = Field(default="HS256", env="JWT_ALGORITHM")
    jwt_expire_hours: int = Field(default=24, env="JWT_EXPIRE_HOURS")

    # Auth0 Configuration
    auth0_domain: Optional[str] = Field(default=None, env="AUTH0_DOMAIN")
    auth0_client_id: Optional[str] = Field(default=None, env="AUTH0_CLIENT_ID")
    auth0_client_secret: Optional[str] = Field(default=None, env="AUTH0_CLIENT_SECRET")
    auth0_callback_url: Optional[str] = Field(default="http://localhost:8000/auth/callback", env="AUTH0_CALLBACK_URL")
    auth0_audience: Optional[str] = Field(default=None, env="AUTH0_AUDIENCE")

    # External User Database
    external_user_db_url: Optional[str] = Field(default=None, env="EXTERNAL_USER_DB_URL")

    # Limits
    max_sessions_per_user: int = Field(default=10, env="MAX_SESSIONS_PER_USER")
    max_messages_per_session: int = Field(default=1000, env="MAX_MESSAGES_PER_SESSION")
    max_message_length: int = Field(default=4000, env="MAX_MESSAGE_LENGTH")
    
    # Tool Calling Configuration
    max_tool_call_iterations: int = Field(default=5, env="MAX_TOOL_CALL_ITERATIONS")
    tool_call_timeout_seconds: int = Field(default=30, env="TOOL_CALL_TIMEOUT_SECONDS")
    

    
    class Config:
        env_file = ".env"
        case_sensitive = False


@lru_cache()
def get_settings() -> Settings:
    """
    Get cached settings instance.
    Uses lru_cache to ensure settings are loaded only once.
    """
    return Settings()


# Global settings instance for convenience
settings = get_settings()