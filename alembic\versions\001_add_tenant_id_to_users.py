"""Add tenant_id to users table

Revision ID: 001_add_tenant_id
Revises: 
Create Date: 2025-01-07 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '001_add_tenant_id'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add tenant_id column to users table
    op.add_column('users', sa.Column('tenant_id', sa.String(100), nullable=True))
    
    # Add index on tenant_id for better query performance
    op.create_index('ix_users_tenant_id', 'users', ['tenant_id'], unique=False)


def downgrade() -> None:
    # Remove index
    op.drop_index('ix_users_tenant_id', table_name='users')
    
    # Remove tenant_id column
    op.drop_column('users', 'tenant_id')