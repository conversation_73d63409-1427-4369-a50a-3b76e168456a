"""
Pinecone search tools for AI agents.
"""

import logging
from typing import Dict, Any, List, Optional

from app.agent.tools.base import BaseTool, ToolSchema, ToolResult, tool_registry
from app.core.config import settings
from app.embedding.service import EmbeddingService
from pinecone import Pinecone

logger = logging.getLogger(__name__)


class PineconeSearchTool(BaseTool):
    """Tool for searching a Pinecone index."""

    def __init__(self):
        super().__init__()
        self._pc = None
        self._index = None
        self._embedding_service = None
        self.index_name = settings.pinecone_index_name

    def _initialize_pinecone(self):
        """Lazy initialization of Pinecone connection."""
        if self._pc is None:
            if not settings.pinecone_api_key:
                raise ValueError("Pinecone API key not configured")
            if not settings.pinecone_index_name:
                raise ValueError("Pinecone index name not configured")

            self._pc = Pinecone(api_key=settings.pinecone_api_key)
            self._index = self._pc.Index(self.index_name)
            self._embedding_service = EmbeddingService()

    @property
    def index(self):
        """Get the Pinecone index with lazy initialization."""
        self._initialize_pinecone()
        return self._index

    @property
    def embedding_service(self):
        """Get the embedding service with lazy initialization."""
        self._initialize_pinecone()
        return self._embedding_service

    @property
    def name(self) -> str:
        return "pinecone_search"

    @property
    def description(self) -> str:
        return "Search for data in a Pinecone index using a text query"

    def get_schema(self) -> ToolSchema:
        return ToolSchema(
            name=self.name,
            description=self.description,
            parameters={
                "type": "object",
                "properties": {
                    "query_text": {
                        "type": "string",
                        "description": "The text to search for",
                    },
                    "top_k": {
                        "type": "integer",
                        "description": "The number of results to return",
                        "default": 5,
                    },
                    "namespace": {
                        "type": "string",
                        "description": "The namespace to search in",
                        "default": None,
                    }
                },
                "required": ["query_text"],
            },
        )

    async def execute(self, user_context: Optional[Dict[str, Any]] = None, **kwargs) -> ToolResult:
        """
        Perform a search in the Pinecone index.

        Args:
            user_context: User context containing user_id, tenant_id, session_key
            query_text: The text to search for.
            top_k: The number of results to return.
            namespace: The namespace to search in.

        Returns:
            ToolResult with search results.
        """
        try:
            # Initialize Pinecone connection if needed
            try:
                self._initialize_pinecone()
            except ValueError as ve:
                return ToolResult(success=False, error=f"Pinecone configuration error: {str(ve)}")

            query_text = kwargs.get("query_text")
            top_k = kwargs.get("top_k", 5)
            namespace = kwargs.get("namespace")

            if not query_text:
                return ToolResult(success=False, error="Query text is required")

            # Get the embedding for the query text
            query_vector = await self.embedding_service.embed_text(query_text)

            results = self.index.query(
                vector=query_vector,
                top_k=top_k,
                include_metadata=True,
                namespace=namespace
            )

            return ToolResult(
                success=True,
                result=results.to_dict(),
            )

        except Exception as e:
            logger.error(f"Pinecone search error: {str(e)}")
            return ToolResult(success=False, error=f"Pinecone search failed: {str(e)}")


# Register tools with error handling
try:
    tool_registry.register_tool(PineconeSearchTool())
    logger.info("Pinecone search tool registered successfully")
except Exception as e:
    logger.warning(f"Failed to register Pinecone search tool: {str(e)}. Tool will be unavailable.")
