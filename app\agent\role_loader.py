from typing import Dict, List, Optional, Any
from pydantic import BaseModel
from sqlalchemy.orm import Session
import logging

from app.core.exceptions import ValidationError

logger = logging.getLogger(__name__)


class RoleConfig(BaseModel):
    """Role configuration model."""
    name: str
    display_name: str
    description: str
    system_prompt: str
    tools: List[str]
    config: Dict[str, Any]
    is_active: bool = True


class RoleLoader:
    """Loads and manages AI role configurations from database only."""

    def __init__(self, db_session: Optional[Session] = None):
        self.db_session = db_session
        self._roles: Dict[str, RoleConfig] = {}
        self._load_all_roles()

    def _load_all_roles(self):
        """Load roles from database only."""
        try:
            if self.db_session:
                self._load_database_roles()
                logger.info(f"Loaded {len(self._roles)} roles from database")
            else:
                logger.warning("No database session available, no roles loaded")

        except Exception as e:
            logger.error(f"Error loading roles: {str(e)}")
            raise ValidationError(f"Error loading roles: {str(e)}")

    def _load_database_roles(self):
        """Load roles from database."""
        try:
            # Import here to avoid circular imports
            from app.models.role import Role

            db_roles = self.db_session.query(Role).filter(Role.is_active == True).all()

            if not db_roles:
                logger.info("No active roles found in database")
                return

            for db_role in db_roles:
                try:
                    role_config = RoleConfig(
                        name=db_role.name,
                        display_name=db_role.display_name,
                        description=db_role.description or "",
                        system_prompt=db_role.system_prompt,
                        tools=db_role.tools or [],
                        config=db_role.config or {},
                        is_active=db_role.is_active
                    )
                    self._roles[db_role.name] = role_config
                    logger.debug(f"Loaded database role: {db_role.name}")
                except Exception as e:
                    logger.error(f"Error loading database role '{db_role.name}': {str(e)}")
                    continue

        except Exception as e:
            logger.error(f"Error loading database roles: {str(e)}")


    
    def get_role(self, role_name: str) -> Optional[RoleConfig]:
        """Get a specific role configuration."""
        return self._roles.get(role_name)
    
    def get_active_roles(self) -> Dict[str, RoleConfig]:
        """Get all active roles."""
        return {name: config for name, config in self._roles.items() if config.is_active}
    
    def list_role_names(self) -> List[str]:
        """Get list of all active role names."""
        return list(self.get_active_roles().keys())
    
    def role_exists(self, role_name: str) -> bool:
        """Check if a role exists and is active."""
        role = self._roles.get(role_name)
        return role is not None and role.is_active
    
    def reload_roles(self):
        """Reload roles from database."""
        self._roles.clear()
        self._load_all_roles()

    def set_database_session(self, db_session: Session):
        """Set database session for loading roles from database."""
        self.db_session = db_session
        # Reload roles with new database session
        self.reload_roles()
    
    def validate_role_tools(self, role_name: str, available_tools: List[str]) -> bool:
        """Validate that all tools required by a role are available."""
        role = self.get_role(role_name)
        if not role:
            return False
        
        return all(tool in available_tools for tool in role.tools)


# Global role loader instance (will be initialized with database session later)
role_loader = RoleLoader()