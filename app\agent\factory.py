import json
import logging
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional

from app.llm.base import <PERSON>MProvider, ChatMessage, FunctionCall, ToolCall
from app.llm.provider_factory import create_llm_provider
from app.agent.role_loader import role_loader, RoleConfig
from app.session.storage import session_storage
from app.agent.tools.tool_manager import tool_manager
from app.core.exceptions import ValidationError, LLMError
from app.core.config import settings

logger = logging.getLogger(__name__)


class AgentMemory:
    """Manages conversation memory for an agent session."""
    
    def __init__(self, session_key: str, max_messages: int = 50):
        self.session_key = session_key
        self.max_messages = max_messages
        self.messages: List[ChatMessage] = []
        self.system_message: Optional[ChatMessage] = None
    
    def set_system_message(self, content: str):
        """Set the system message for the agent."""
        self.system_message = ChatMessage(
            role="system",
            content=content
        )
    
    def add_message(self, role: str, content: str, metadata: Dict[str, Any] = None):
        """Add a message to the conversation history."""
        message = ChatMessage(
            role=role,
            content=content,
            metadata=metadata or {}
        )
        
        self.messages.append(message)
        
        # Keep only the most recent messages
        if len(self.messages) > self.max_messages:
            self.messages = self.messages[-self.max_messages:]
    
    def get_conversation_messages(self) -> List[ChatMessage]:
        """Get all messages for the LLM, including system message."""
        conversation = []
        
        if self.system_message:
            conversation.append(self.system_message)
        
        conversation.extend(self.messages)
        return conversation
    
    def clear_history(self):
        """Clear conversation history (but keep system message)."""
        self.messages.clear()
    
    async def save_to_storage(self):
        """Save conversation history to storage."""
        try:
            messages_data = [
                {
                    "role": msg.role,
                    "content": msg.content,
                    "metadata": msg.metadata or {},
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }
                for msg in self.messages
            ]

            session_storage.save_session_messages(
                self.session_key,
                messages_data,
                max_messages=self.max_messages
            )
        except Exception as e:
            logger.error(f"Failed to save messages for session {self.session_key}: {str(e)}")

    async def load_from_storage(self):
        """Load conversation history from storage."""
        try:
            messages_data = session_storage.get_session_messages(self.session_key)

            self.messages.clear()
            for msg_data in messages_data:
                self.messages.append(ChatMessage(
                    role=msg_data["role"],
                    content=msg_data["content"],
                    metadata=msg_data.get("metadata", {})
                ))
        except Exception as e:
            logger.error(f"Failed to load messages for session {self.session_key}: {str(e)}")
            # Continue with empty message history


class AIAgent:
    """AI Agent that combines a role configuration with an LLM provider."""
    
    def __init__(
        self,
        session_key: str,
        role_config: RoleConfig,
        llm_provider: LLMProvider,
        memory: AgentMemory,
        user_context: Optional[Dict[str, Any]] = None
    ):
        self.session_key = session_key
        self.role_config = role_config
        self.llm_provider = llm_provider
        self.memory = memory
        self.user_context = user_context or {}
        
        # Set up system message
        self.memory.set_system_message(role_config.system_prompt)
    
    async def generate_response(self, user_message: str) -> str:
        """
        Generate a response to a user message with function calling support.

        Args:
            user_message: The user's input message

        Returns:
            str: The agent's response

        Raises:
            LLMError: If response generation fails
        """
        try:
            # Add user message to memory
            self.memory.add_message("user", user_message)

            # Get conversation messages
            conversation = self.memory.get_conversation_messages()

            # Get available tools for this role
            tools = self._get_tools_for_role()

            # Generate response using LLM with tools
            llm_config = self.role_config.config.copy()
            response = await self.llm_provider.generate_response(
                conversation,
                tools=tools,
                **llm_config
            )

            # Handle function calls if present
            final_response = await self._handle_function_calls(response, conversation, tools, llm_config)

            # Add final assistant response to memory
            self.memory.add_message(
                "assistant",
                final_response,
                metadata=response.metadata
            )

            # Save conversation to storage and update session metadata
            await self._update_session_state()

            return final_response

        except Exception as e:
            raise LLMError(f"Failed to generate response: {str(e)}")

    def _get_tools_for_role(self) -> List[Dict[str, Any]]:
        """Get available tools/functions for this role."""
        if not self.role_config.tools:
            return []

        # Get tool schemas for the role's tools using the unified tool manager
        schemas, missing_tools = tool_manager.get_function_schemas_for_role(self.role_config.tools)
        
        if missing_tools:
            logger.warning(f"Missing tools for role {self.role_config.name}: {missing_tools}")
        
        return schemas

    async def _handle_function_calls(
        self,
        response,
        conversation: List[ChatMessage],
        tools: List[Dict[str, Any]],
        llm_config: Dict[str, Any]
    ) -> str:
        """Handle function calls in the LLM response with loop support."""
        # If no function calls, return the content directly
        if not response.tool_calls and not response.function_call:
            return response.content or ""

        # Initialize loop variables
        max_iterations = settings.max_tool_call_iterations
        current_iteration = 0
        updated_conversation = conversation.copy()
        
        # Track the current response being processed
        current_response = response
        
        # Start the tool calling loop
        while current_iteration < max_iterations:
            current_iteration += 1
            logger.debug(f"Tool calling iteration {current_iteration}/{max_iterations}")
            
            # Check if current response has function calls
            if not current_response.tool_calls and not current_response.function_call:
                # No more function calls, return the response content
                return current_response.content or ""
            
            # Collect function calls from both formats
            function_calls = []
            if current_response.function_call:
                function_calls.append(current_response.function_call)
            if current_response.tool_calls:
                function_calls.extend([tool_call.function for tool_call in current_response.tool_calls])

            # Execute all function calls using the unified tool manager
            function_results = []
            for func_call in function_calls:
                try:
                    import json
                    args = json.loads(func_call.arguments) if func_call.arguments else {}
                    result = await tool_manager.execute_tool(func_call.name, args, user_context=self.user_context)
                    function_results.append({
                        "name": func_call.name,
                        "result": result.result if result.success else f"Error: {result.error}",
                        "success": result.success
                    })
                    logger.debug(f"Executed tool {func_call.name}: success={result.success}")
                except Exception as e:
                    logger.error(f"Error executing tool {func_call.name}: {e}")
                    function_results.append({
                        "name": func_call.name,
                        "result": f"Error: {str(e)}",
                        "success": False
                    })

            # Add assistant message with function call(s)
            updated_conversation.append(ChatMessage(
                role="assistant",
                content=current_response.content,
                tool_calls=current_response.tool_calls,
                function_call=current_response.function_call
            ))

            # Add function/tool responses
            if current_response.tool_calls:
                # New tool call format
                for tool_call, func_result in zip(current_response.tool_calls, function_results):
                    updated_conversation.append(ChatMessage(
                        role="tool",
                        content=json.dumps(func_result),
                        tool_call_id=tool_call.id,
                        name=func_result["name"]
                    ))
            elif current_response.function_call:
                # Legacy function call format
                updated_conversation.append(ChatMessage(
                    role="function",
                    content=json.dumps(function_results[0]),
                    name=function_results[0]["name"]
                ))

            # Get next response from LLM based on the updated conversation
            try:
                next_response = await self.llm_provider.generate_response(
                    updated_conversation,
                    tools=tools,
                    **llm_config
                )
                current_response = next_response
                logger.debug(f"Generated response for iteration {current_iteration}: has_tool_calls={bool(next_response.tool_calls or next_response.function_call)}")
            except Exception as e:
                logger.error(f"Error generating response in iteration {current_iteration}: {e}")
                # Return the last successful response content
                return current_response.content or ""

        # If we've reached max iterations, log a warning and return the last response
        logger.warning(f"Reached maximum tool call iterations ({max_iterations}), stopping loop")
        return current_response.content or ""

    async def generate_streaming_response(self, user_message: str, websocket_callback=None):
        """
        Generate a streaming response to a user message.

        Args:
            user_message: The user's input message
            websocket_callback: Optional callback function for WebSocket notifications

        Yields:
            str: Streaming response chunks

        Raises:
            LLMError: If response generation fails
        """
        try:
            # Add user message to memory
            self.memory.add_message("user", user_message)

            # Get conversation messages
            conversation = self.memory.get_conversation_messages()

            # Get available tools for this role
            tools = self._get_tools_for_role()

            # Generate streaming response with tools
            llm_config = self.role_config.config.copy()
            full_response = ""

            # Collect tool calls from streaming chunks
            collected_tool_calls = []
            collected_function_call = None
            tool_call_chunks = {}  # For accumulating tool call data across chunks
            current_tool_call_id = None  # Track the current tool call ID

            async for chunk in self.llm_provider.generate_stream(
                conversation,
                tools=tools,
                **llm_config
            ):
                # Handle content chunks
                if chunk.content:
                    full_response += chunk.content
                    yield chunk.content

                # Handle tool calls in streaming chunks
                if chunk.tool_calls:
                    logger.debug(f"Received tool calls in chunk: {chunk.tool_calls}")
                    for tool_call in chunk.tool_calls:
                        logger.debug(f"Processing tool call: id={tool_call.id}, name={tool_call.function.name}, args={tool_call.function.arguments}")

                        # Use the first non-empty ID as the current tool call ID
                        if tool_call.id and not current_tool_call_id:
                            current_tool_call_id = tool_call.id

                        # Use current_tool_call_id for accumulation (handles empty IDs in subsequent chunks)
                        accumulation_id = current_tool_call_id if current_tool_call_id else tool_call.id

                        if accumulation_id not in tool_call_chunks:
                            tool_call_chunks[accumulation_id] = {
                                "id": accumulation_id,
                                "type": tool_call.type,
                                "function": {
                                    "name": "",
                                    "arguments": ""
                                }
                            }

                        # Accumulate function name and arguments (avoid duplicates)
                        if tool_call.function.name:
                            current_name = tool_call_chunks[accumulation_id]["function"]["name"]
                            new_name_part = tool_call.function.name
                            # Only append if this chunk doesn't already exist in the accumulated name
                            if not current_name.endswith(new_name_part):
                                tool_call_chunks[accumulation_id]["function"]["name"] += new_name_part
                            logger.debug(f"Accumulated name: {tool_call_chunks[accumulation_id]['function']['name']}")
                        if tool_call.function.arguments:
                            current_args = tool_call_chunks[accumulation_id]["function"]["arguments"]
                            new_args_part = tool_call.function.arguments
                            # Only append if this chunk doesn't already exist in the accumulated arguments
                            if not current_args.endswith(new_args_part):
                                tool_call_chunks[accumulation_id]["function"]["arguments"] += new_args_part
                            logger.debug(f"Accumulated args: {tool_call_chunks[accumulation_id]['function']['arguments']}")

                # Handle legacy function calls
                if chunk.function_call:
                    if not collected_function_call:
                        collected_function_call = {"name": "", "arguments": ""}
                    if chunk.function_call.name:
                        current_name = collected_function_call["name"]
                        new_name_part = chunk.function_call.name
                        # Only append if this chunk doesn't already exist in the accumulated name
                        if not current_name.endswith(new_name_part):
                            collected_function_call["name"] += new_name_part
                    if chunk.function_call.arguments:
                        current_args = collected_function_call["arguments"]
                        new_args_part = chunk.function_call.arguments
                        # Only append if this chunk doesn't already exist in the accumulated arguments
                        if not current_args.endswith(new_args_part):
                            collected_function_call["arguments"] += new_args_part

                if chunk.is_complete:
                    break

            # Process collected tool calls if any
            has_valid_tool_calls = False

            # Convert accumulated tool calls to proper format
            if tool_call_chunks:
                for tool_call_data in tool_call_chunks.values():
                    # Only create tool call if we have a valid function name
                    if tool_call_data["function"]["name"]:
                        collected_tool_calls.append(ToolCall(
                            id=tool_call_data["id"],
                            type=tool_call_data["type"],
                            function=FunctionCall(
                                name=tool_call_data["function"]["name"],
                                arguments=tool_call_data["function"]["arguments"]
                            )
                        ))
                        has_valid_tool_calls = True

            # Check if we have a valid function call
            function_call_obj = None
            if collected_function_call and collected_function_call["name"]:
                function_call_obj = FunctionCall(
                    name=collected_function_call["name"],
                    arguments=collected_function_call["arguments"]
                )
                has_valid_tool_calls = True

            # Only process tool calls if we have valid ones
            if has_valid_tool_calls:
                logger.debug(f"Processing {len(collected_tool_calls)} tool calls")

                # Initialize loop variables for streaming tool calls
                max_iterations = settings.max_tool_call_iterations
                current_iteration = 0
                
                # Start the tool calling loop for streaming
                while current_iteration < max_iterations:
                    current_iteration += 1
                    logger.debug(f"Streaming tool calling iteration {current_iteration}/{max_iterations}")
                    
                    # Add assistant message with tool calls to conversation
                    from app.llm.base import ChatMessage
                    if collected_tool_calls:
                        conversation.append(ChatMessage(
                            role="assistant",
                            content=full_response,
                            tool_calls=collected_tool_calls
                        ))
                    elif function_call_obj:
                        conversation.append(ChatMessage(
                            role="assistant",
                            content=full_response,
                            function_call=function_call_obj
                        ))

                    # Execute tool calls and add results to conversation
                    for tool_call in collected_tool_calls:
                        try:
                            import json
                            args = json.loads(tool_call.function.arguments) if tool_call.function.arguments else {}
                            
                            # Notify WebSocket about tool call if callback provided
                            if websocket_callback:
                                await websocket_callback({
                                    "type": "tool.call.start",
                                    "tool_name": tool_call.function.name,
                                    "arguments": tool_call.function.arguments,
                                    "iteration": current_iteration
                                })
                            
                            result = await tool_manager.execute_tool(tool_call.function.name, args, user_context=self.user_context)
                            
                            # Notify WebSocket about tool result if callback provided
                            if websocket_callback:
                                await websocket_callback({
                                    "type": "tool.call.result",
                                    "tool_name": tool_call.function.name,
                                    "arguments": tool_call.function.arguments,
                                    "result": result,
                                    "iteration": current_iteration
                                })

                            # Add tool result to conversation
                            conversation.append(ChatMessage(
                                role="tool",
                                content=json.dumps({
                                    "name": tool_call.function.name,
                                    "result": result.result if result.success else f"Error: {result.error}",
                                    "success": result.success
                                }),
                                tool_call_id=tool_call.id,
                                name=tool_call.function.name
                            ))

                        except Exception as e:
                            logger.error(f"Error executing tool {tool_call.function.name}: {e}")
                            
                            # Notify WebSocket about tool error if callback provided
                            if websocket_callback:
                                await websocket_callback({
                                    "type": "tool.call.error",
                                    "tool_name": tool_call.function.name,
                                    "arguments": tool_call.function.arguments if 'tool_call' in locals() else "",
                                    "error": str(e),
                                    "iteration": current_iteration
                                })
                            
                            # Add error result to conversation
                            conversation.append(ChatMessage(
                                role="tool",
                                content=json.dumps({
                                    "name": tool_call.function.name,
                                    "result": f"Error: {str(e)}",
                                    "success": False
                                }),
                                tool_call_id=tool_call.id,
                                name=tool_call.function.name
                            ))

                    # Generate next response to check for more tool calls
                    logger.debug(f"Generating response for iteration {current_iteration}")
                    try:
                        next_response = ""
                        next_tool_calls = []
                        next_tool_call_chunks = {}
                        next_current_tool_call_id = None
                        
                        async for chunk in self.llm_provider.generate_stream(
                            conversation,
                            tools=tools,
                            **llm_config
                        ):
                            # Handle content chunks
                            if chunk.content:
                                next_response += chunk.content
                                # Don't yield content chunks during tool call iterations
                                
                            # Handle tool calls in streaming chunks
                            if chunk.tool_calls:
                                for tool_call in chunk.tool_calls:
                                    # Use the first non-empty ID as the current tool call ID
                                    if tool_call.id and not next_current_tool_call_id:
                                        next_current_tool_call_id = tool_call.id

                                    # Use current_tool_call_id for accumulation
                                    accumulation_id = next_current_tool_call_id if next_current_tool_call_id else tool_call.id

                                    if accumulation_id not in next_tool_call_chunks:
                                        next_tool_call_chunks[accumulation_id] = {
                                            "id": accumulation_id,
                                            "type": tool_call.type,
                                            "function": {
                                                "name": "",
                                                "arguments": ""
                                            }
                                        }

                                    # Accumulate function name and arguments (avoid duplicates)
                                    if tool_call.function.name:
                                        current_name = next_tool_call_chunks[accumulation_id]["function"]["name"]
                                        new_name_part = tool_call.function.name
                                        # Only append if this chunk doesn't already exist in the accumulated name
                                        if not current_name.endswith(new_name_part):
                                            next_tool_call_chunks[accumulation_id]["function"]["name"] += new_name_part
                                    if tool_call.function.arguments:
                                        current_args = next_tool_call_chunks[accumulation_id]["function"]["arguments"]
                                        new_args_part = tool_call.function.arguments
                                        # Only append if this chunk doesn't already exist in the accumulated arguments
                                        if not current_args.endswith(new_args_part):
                                            next_tool_call_chunks[accumulation_id]["function"]["arguments"] += new_args_part

                            if chunk.is_complete:
                                break
                        
                        # Convert accumulated tool calls to proper format
                        next_tool_calls = []
                        if next_tool_call_chunks:
                            for tool_call_data in next_tool_call_chunks.values():
                                if tool_call_data["function"]["name"]:
                                    next_tool_calls.append(ToolCall(
                                        id=tool_call_data["id"],
                                        type=tool_call_data["type"],
                                        function=FunctionCall(
                                            name=tool_call_data["function"]["name"],
                                            arguments=tool_call_data["function"]["arguments"]
                                        )
                                    ))
                        
                        # Check if we have more tool calls to process
                        if next_tool_calls:
                            # Update for next iteration
                            collected_tool_calls = next_tool_calls
                            full_response = next_response
                            logger.debug(f"Found {len(next_tool_calls)} more tool calls, continuing loop")
                        else:
                            # No more tool calls, stream the final response and exit loop
                            logger.debug("No more tool calls found, streaming final response")
                            for char in next_response:
                                yield char
                            full_response += next_response
                            break
                            
                    except Exception as e:
                        logger.error(f"Error in streaming tool calling iteration {current_iteration}: {e}")
                        break
                
                # If we've reached max iterations, log a warning
                if current_iteration >= max_iterations:
                    logger.warning(f"Reached maximum tool call iterations ({max_iterations}) in streaming mode")
                    
            else:
                # No tool calls, just return the content
                logger.debug("No tool calls to process")

            # Add complete assistant response to memory
            if full_response:
                self.memory.add_message(
                    "assistant",
                    full_response,
                    metadata={"streaming": True}
                )

                # Save conversation to storage and update session metadata
                await self._update_session_state()

        except Exception as e:
            raise LLMError(f"Failed to generate streaming response: {str(e)}")

    async def _update_session_state(self):
        """Update session state in storage."""
        try:
            await self.memory.save_to_storage()
            session_storage.update_session_metadata(self.session_key, {
                "last_activity": datetime.now(timezone.utc).isoformat(),
                "message_count": len(self.memory.messages)
            })
        except Exception as e:
            logger.error(f"Failed to update session state for {self.session_key}: {str(e)}")

    def get_role_info(self) -> Dict[str, Any]:
        """Get information about the agent's role."""
        return {
            "role_name": self.role_config.display_name,
            "description": self.role_config.description,
            "tools": self.role_config.tools or [],
            "message_count": len(self.memory.messages)
        }


class AgentFactory:
    """Factory for creating AI agents with specific roles."""

    def __init__(self):
        self._active_agents: Dict[str, AIAgent] = {}
        self._role_cache: Dict[str, RoleConfig] = {}
    
    async def create_agent(
        self,
        session_key: str,
        role_name: str,
        llm_provider: Optional[LLMProvider] = None,
        user_context: Optional[Dict[str, Any]] = None
    ) -> AIAgent:
        """
        Create or retrieve an AI agent for a session.
        
        Args:
            session_key: Unique session identifier
            role_name: Name of the role to assign
            llm_provider: Optional custom LLM provider
            user_context: Optional user context containing user_id, tenant_id, session_key
            
        Returns:
            AIAgent: Configured AI agent
            
        Raises:
            ValidationError: If role doesn't exist
            LLMError: If LLM provider setup fails
        """
        # Check if agent already exists for this session
        if session_key in self._active_agents:
            return self._active_agents[session_key]
        
        # Get role configuration (with caching)
        role_config = self._get_role_config(role_name)
        if not role_config or not role_config.is_active:
            raise ValidationError(f"Role '{role_name}' not found or inactive")
        
        # Create LLM provider if not provided
        if llm_provider is None:
            llm_provider = create_llm_provider(config=role_config.config)

        # Validate LLM connection (log warnings but don't fail agent creation)
        try:
            connection_valid = await llm_provider.validate_connection()
            if not connection_valid:
                logger.warning(f"LLM connection validation failed for session {session_key}")
        except Exception as e:
            logger.warning(f"LLM connection validation error for session {session_key}: {str(e)}")
        
        # Create memory manager
        memory = AgentMemory(
            session_key=session_key,
            max_messages=settings.max_messages_per_session
        )
        
        # Load existing conversation from storage
        await memory.load_from_storage()
        
        # Create agent
        agent = AIAgent(
            session_key=session_key,
            role_config=role_config,
            llm_provider=llm_provider,
            memory=memory,
            user_context=user_context
        )
        
        # Cache the agent
        self._active_agents[session_key] = agent
        
        return agent
    
    async def get_agent(self, session_key: str) -> Optional[AIAgent]:
        """Get an existing agent for a session."""
        return self._active_agents.get(session_key)
    
    async def remove_agent(self, session_key: str) -> bool:
        """Remove an agent from memory (usually when session is deleted)."""
        if session_key in self._active_agents:
            del self._active_agents[session_key]
            return True
        return False
    
    def get_active_sessions(self) -> List[str]:
        """Get list of session keys with active agents."""
        return list(self._active_agents.keys())
    
    def _get_role_config(self, role_name: str) -> Optional[RoleConfig]:
        """Get role configuration with caching."""
        if role_name not in self._role_cache:
            self._role_cache[role_name] = role_loader.get_role(role_name)
        return self._role_cache[role_name]

    def clear_role_cache(self):
        """Clear the role configuration cache."""
        self._role_cache.clear()

    async def cleanup_inactive_agents(self, active_session_keys: List[str]):
        """Remove agents for sessions that are no longer active."""
        current_sessions = set(self._active_agents.keys())
        active_sessions = set(active_session_keys)

        inactive_sessions = current_sessions - active_sessions

        for session_key in inactive_sessions:
            await self.remove_agent(session_key)

        logger.info(f"Cleaned up {len(inactive_sessions)} inactive agents")


# Global agent factory instance
agent_factory = AgentFactory()