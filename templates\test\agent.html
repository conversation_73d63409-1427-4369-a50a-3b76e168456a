{% extends "base.html" %}

{% block title %}Test Agent - Xapa AI{% endblock %}

{% block extra_head %}
<style>
.message.streaming .typing-indicator {
    animation: blink 1s infinite;
    color: var(--primary-color);
    font-weight: bold;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}


.message.system {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    text-align: center;
    font-style: italic;
    margin: 0 auto;
    max-width: 60%;
    border-radius: 0.5rem;
}

.message.tool {
    background: var(--primary-alpha-10);
    border: 1px solid var(--primary-alpha-25);
    border-left: 4px solid var(--primary-color);
    margin: 0.5rem auto;
    max-width: 90%;
    border-radius: 0.75rem;
    box-shadow: 0 2px 8px var(--primary-alpha-15);
}

.tool-call-info {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 0.5rem;
    margin: 0.5rem 0;
    font-family: monospace;
    font-size: 0.875rem;
}

.tool-call-header {
    cursor: pointer;
    user-select: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.25rem 0;
}

.tool-call-header:hover {
    background-color: var(--primary-alpha-10);
    border-radius: 0.25rem;
}

.tool-call-content {
    margin-top: 0.5rem;
    padding-top: 0.5rem;
    border-top: 1px solid #dee2e6;
}

.tool-call-content.collapsed {
    display: none;
}

.collapse-icon {
    transition: transform 0.2s ease;
}

.collapse-icon.collapsed {
    transform: rotate(-90deg);
}

.tool-indicator {
    background: var(--gradient-primary);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    margin-bottom: 0.5rem;
    display: inline-block;
    font-weight: 600;
}

.message-time {
    font-size: 0.75rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

/* Markdown styling for messages */
.message-content h1, .message-content h2, .message-content h3, 
.message-content h4, .message-content h5, .message-content h6 {
    margin-top: 1rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.message-content h1 { font-size: 1.5rem; }
.message-content h2 { font-size: 1.3rem; }
.message-content h3 { font-size: 1.1rem; }
.message-content h4 { font-size: 1rem; }

.message-content code {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 0.25rem;
    padding: 0.1rem 0.3rem;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    color: #e83e8c;
}

.message-content pre {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    padding: 1rem;
    margin: 0.5rem 0;
    overflow-x: auto;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    line-height: 1.4;
}

.message-content pre code {
    background: none;
    border: none;
    padding: 0;
    color: inherit;
}

.message-content blockquote {
    border-left: 4px solid var(--primary-color);
    background-color: #f8f9fa;
    margin: 0.5rem 0;
    padding: 0.5rem 1rem;
    border-radius: 0 0.25rem 0.25rem 0;
}

.message-content ul, .message-content ol {
    margin: 0.5rem 0;
    padding-left: 1.5rem;
}

.message-content li {
    margin: 0.25rem 0;
}

.message-content table {
    width: 100%;
    border-collapse: collapse;
    margin: 0.5rem 0;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    overflow: hidden;
}

.message-content th, .message-content td {
    padding: 0.5rem;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

.message-content th {
    background-color: #f8f9fa;
    font-weight: 600;
}

.message-content a {
    color: var(--primary-color);
    text-decoration: none;
}

.message-content a:hover {
    text-decoration: underline;
}

.message-content strong {
    font-weight: 600;
}

.message-content em {
    font-style: italic;
}

.message-content hr {
    border: none;
    border-top: 1px solid #dee2e6;
    margin: 1rem 0;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-cog me-2"></i>
                        Configuration
                    </h6>
                </div>
                <div class="card-body">
                    <!-- Role Selection -->
                    <div class="mb-3">
                        <label for="roleSelect" class="form-label">Select Role</label>
                        <select class="form-select" id="roleSelect" onchange="loadRole()">
                            <option value="">Choose a role...</option>
                        </select>
                    </div>
                    
                    <!-- Role Info -->
                    <div id="roleInfo" style="display: none;">
                        <div class="mb-3">
                            <label class="form-label">Role Details</label>
                            <div class="card bg-light">
                                <div class="card-body p-2">
                                    <h6 id="roleDisplayName" class="mb-1"></h6>
                                    <p id="roleDescription" class="text-muted small mb-2"></p>
                                    <div id="roleTools" class="d-flex flex-wrap gap-1">
                                        <!-- Tools badges will be added here -->
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Configuration -->
                        <div class="mb-3">
                            <label for="temperature" class="form-label">Temperature</label>
                            <input type="range" class="form-range" id="temperature" 
                                   min="0" max="2" step="0.1" value="0.7">
                            <div class="d-flex justify-content-between">
                                <small>0.0</small>
                                <small id="temperatureValue">0.7</small>
                                <small>2.0</small>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="maxTokens" class="form-label">Max Tokens</label>
                            <input type="number" class="form-control" id="maxTokens" 
                                   min="1" max="4096" value="1000">
                        </div>
                    </div>
                    
                    <!-- Actions -->
                    <div class="d-grid gap-2">
                        <button class="btn btn-success" onclick="startNewSession()" disabled id="startBtn">
                            <i class="fas fa-play me-1"></i>
                            Start Session
                        </button>
                        <button class="btn btn-warning" onclick="clearChat()" id="clearBtn">
                            <i class="fas fa-trash me-1"></i>
                            Clear Chat
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Session Info -->
            <div class="card mt-3" id="sessionCard" style="display: none;">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Session Info
                    </h6>
                </div>
                <div class="card-body">
                    <p class="mb-1"><strong>Session ID:</strong></p>
                    <p class="text-muted small" id="sessionId">-</p>
                    <p class="mb-1"><strong>Messages:</strong></p>
                    <p class="text-muted small" id="messageCount">0</p>
                    <p class="mb-1"><strong>Started:</strong></p>
                    <p class="text-muted small" id="sessionStarted">-</p>
                </div>
            </div>
        </div>
        
        <!-- Chat Area -->
        <div class="col-md-9">
            <div class="card h-100">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-comments me-2"></i>
                            Chat with AI Agent
                            <span id="toolCallIndicator" class="badge bg-info ms-2" style="display: none;">
                                <i class="fas fa-cog fa-spin me-1"></i>Tool Calling
                            </span>
                        </h5>
                        <div id="statusIndicator" class="badge bg-secondary">
                            Not Connected
                        </div>
                    </div>
                </div>
                <div class="card-body d-flex flex-column p-0">
                    <!-- Chat Messages -->
                    <div class="chat-container flex-grow-1 p-3" id="chatContainer">
                        <div class="text-center text-muted py-5">
                            <i class="fas fa-robot fa-3x mb-3"></i>
                            <h5>Welcome to AI Agent Testing</h5>
                            <p>Select a role and start a session to begin chatting with the AI agent.</p>
                        </div>
                    </div>
                    
                    <!-- Chat Input -->
                    <div class="border-top p-3">
                        <form id="chatForm" onsubmit="sendMessage(event)">
                            <div class="input-group">
                                <input type="text" class="form-control" id="messageInput" 
                                       placeholder="Type your message..." disabled>
                                <button class="btn btn-primary" type="submit" disabled id="sendBtn">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2 mb-0" id="loadingMessage">Loading...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<!-- Markdown parsing library -->
<script src="https://cdn.jsdelivr.net/npm/marked@9.1.2/marked.min.js"></script>
<script>
let currentSession = null;
let currentRole = null;
let messageCount = 0;
let websocket = null;
let currentStreamingMessage = null;

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadRoles();
    setupEventListeners();
    
    // Check if role is pre-selected from URL
    const urlParams = new URLSearchParams(window.location.search);
    const selectedRole = urlParams.get('role');
    if (selectedRole) {
        document.getElementById('roleSelect').value = selectedRole;
        loadRole();
    }
});

// Setup event listeners
function setupEventListeners() {
    // Temperature slider
    const temperatureSlider = document.getElementById('temperature');
    temperatureSlider.addEventListener('input', function() {
        document.getElementById('temperatureValue').textContent = this.value;
    });
}

// Load available roles
async function loadRoles() {
    try {
        const response = await utils.apiGet('/roles?active_only=true');
        const roleSelect = document.getElementById('roleSelect');
        
        // Clear existing options except the first one
        roleSelect.innerHTML = '<option value="">Choose a role...</option>';
        
        response.roles.forEach(role => {
            const option = document.createElement('option');
            option.value = role.name;
            option.textContent = role.display_name;
            roleSelect.appendChild(option);
        });
        
    } catch (error) {
        console.error('Failed to load roles:', error);
        utils.showAlert('Failed to load roles: ' + error.message, 'danger');
    }
}

// Load selected role
async function loadRole() {
    const roleName = document.getElementById('roleSelect').value;
    if (!roleName) {
        document.getElementById('roleInfo').style.display = 'none';
        document.getElementById('startBtn').disabled = true;
        return;
    }
    
    try {
        const response = await utils.apiGet(`/roles/${roleName}`);
        currentRole = response;
        
        // Update role info
        document.getElementById('roleDisplayName').textContent = response.display_name;
        document.getElementById('roleDescription').textContent = response.description || 'No description';
        
        // Update tools
        const toolsContainer = document.getElementById('roleTools');
        toolsContainer.innerHTML = response.tools.map(tool => 
            `<span class="badge bg-secondary">${utils.escapeHtml(tool)}</span>`
        ).join('');
        
        // Update configuration
        if (response.config) {
            document.getElementById('temperature').value = response.config.temperature || 0.7;
            document.getElementById('temperatureValue').textContent = response.config.temperature || 0.7;
            document.getElementById('maxTokens').value = response.config.max_tokens || 1000;
        }
        
        document.getElementById('roleInfo').style.display = 'block';
        document.getElementById('startBtn').disabled = false;
        
    } catch (error) {
        console.error('Failed to load role:', error);
        utils.showAlert('Failed to load role: ' + error.message, 'danger');
    }
}

// Start new session
async function startNewSession() {
    if (!currentRole) return;
    
    try {
        utils.showLoading(true, 'Starting session...');
        
        const sessionData = {
            name: `Session - ${currentRole.display_name} - ${new Date().toLocaleString()}`,
            role_name: currentRole.name,
            config: {
                temperature: parseFloat(document.getElementById('temperature').value),
                max_tokens: parseInt(document.getElementById('maxTokens').value)
            }
        };
        
        const response = await utils.apiPost('/sessions', sessionData);
        currentSession = response;
        messageCount = 0;
        
        // Update UI
        document.getElementById('sessionId').textContent = response.id;
        document.getElementById('messageCount').textContent = '0';
        document.getElementById('sessionStarted').textContent = utils.formatDate(response.created_at);
        document.getElementById('sessionCard').style.display = 'block';
        document.getElementById('statusIndicator').textContent = 'Connected';
        document.getElementById('statusIndicator').className = 'badge bg-success';
        
        // Connect WebSocket for streaming
        await connectWebSocket();

        // Enable chat
        document.getElementById('messageInput').disabled = false;
        document.getElementById('sendBtn').disabled = false;

        // Clear chat and add welcome message
        clearChatMessages();
        addMessage('system', `Session started with role: ${currentRole.display_name} (Streaming enabled)`);
        

        utils.showAlert('Session started successfully with streaming', 'success', 2000);
        
    } catch (error) {
        console.error('Failed to start session:', error);
        utils.showAlert('Failed to start session: ' + error.message, 'danger');
    } finally {
        utils.showLoading(false);
    }
}

// WebSocket connection management
async function connectWebSocket() {
    if (websocket && websocket.readyState === WebSocket.OPEN) {
        return; // Already connected
    }

    try {
        // Note: WebSocket authentication will need to be updated to use Auth0 tokens
        // For now, this will fail since anonymous tokens are no longer available
        console.error('WebSocket authentication needs to be updated for Auth0');
        throw new Error('WebSocket authentication not configured for Auth0');

        const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${wsProtocol}//${window.location.host}/ws/chat?session_key=${currentSession.session_key}&token=AUTH0_TOKEN_NEEDED`;

        websocket = new WebSocket(wsUrl);

        websocket.onopen = function(event) {
            console.log('WebSocket connected');
            document.getElementById('statusIndicator').textContent = 'Connected (Streaming)';
            document.getElementById('statusIndicator').className = 'badge bg-success';
        };

        websocket.onmessage = function(event) {
            handleWebSocketMessage(JSON.parse(event.data));
        };

        websocket.onclose = function(event) {
            console.log('WebSocket disconnected');
            document.getElementById('statusIndicator').textContent = 'Disconnected';
            document.getElementById('statusIndicator').className = 'badge bg-danger';
            websocket = null;
        };

        websocket.onerror = function(error) {
            console.error('WebSocket error:', error);
            addMessage('system', 'WebSocket connection error. Please try again.');
        };

    } catch (error) {
        console.error('Failed to connect WebSocket:', error);
        utils.showAlert('Failed to connect WebSocket: ' + error.message, 'danger');
    }
}

function handleWebSocketMessage(data) {
    switch (data.type) {
        case 'connection.established':
            console.log('WebSocket connection established');
            break;

        case 'chat.message.start':
            // Start a new streaming message
            currentStreamingMessage = {
                id: data.message_id,
                content: '',
                element: null,
                tool_calls: [],
                toolCallsInserted: false
            };
            // Don't add the assistant message yet - wait for tool calls or first content
            break;
            
        case 'tool.call':
            // Handle tool call information
            if (currentStreamingMessage && data.message_id === currentStreamingMessage.id) {
                // Show tool calling indicator
                document.getElementById('toolCallIndicator').style.display = 'inline-block';
                
                // Store tool call info
                currentStreamingMessage.tool_calls.push({
                    name: data.tool_name,
                    arguments: data.arguments,
                    result: data.result
                });
                
                // Add tool call message
                addToolCallMessage(data.tool_name, data.arguments, data.result);
            }
            break;

        case 'tool.call.start':
            // Handle tool call start
            if (currentStreamingMessage && data.message_id === currentStreamingMessage.id) {
                // Show tool calling indicator
                document.getElementById('toolCallIndicator').style.display = 'inline-block';
                
                // Add tool call start message
                addToolCallStartMessage(data.metadata.tool_name, data.metadata.arguments);
                currentStreamingMessage.toolCallsInserted = true;
            }
            break;
            
        case 'tool.call.result':
            // Handle tool call result
            if (currentStreamingMessage && data.message_id === currentStreamingMessage.id) {
                // Update the tool call message with result
                updateToolCallResult(data.metadata.tool_name, data.metadata.result);
            }
            break;
            
        case 'tool.call.error':
            // Handle tool call error
            if (currentStreamingMessage && data.message_id === currentStreamingMessage.id) {
                // Update the tool call message with error
                updateToolCallError(data.metadata.tool_name, data.metadata.error);
            }
            break;

        case 'chat.message.chunk':
            // Append chunk to current streaming message
            if (currentStreamingMessage && data.message_id === currentStreamingMessage.id) {
                // If this is the first content and we haven't added the assistant message yet, add it now
                if (!currentStreamingMessage.element && data.content) {
                    addStreamingMessage('assistant', '');
                }
                
                currentStreamingMessage.content += data.content || '';
                updateStreamingMessage(currentStreamingMessage.content);
            }
            break;

        case 'chat.message.end':
            // Finalize streaming message
            if (currentStreamingMessage && data.message_id === currentStreamingMessage.id) {
                // If we never added the assistant message (e.g., only tool calls), add it now
                if (!currentStreamingMessage.element) {
                    addStreamingMessage('assistant', currentStreamingMessage.content || 'Tool execution completed.');
                }
                
                finalizeStreamingMessage();
                messageCount += 2;
                document.getElementById('messageCount').textContent = messageCount.toString();
                
                // Hide tool calling indicator
                document.getElementById('toolCallIndicator').style.display = 'none';
                
                currentStreamingMessage = null;
            }
            break;

        case 'error':
            console.error('WebSocket error:', data.metadata?.error);
            addMessage('system', `Error: ${data.metadata?.error || 'Unknown error'}`);
            break;

        case 'pong':
            // Handle ping/pong for connection keep-alive
            break;

        default:
            console.log('Unknown WebSocket message type:', data.type);
    }
}

// Send message via WebSocket
async function sendMessage(event) {
    event.preventDefault();

    const input = document.getElementById('messageInput');
    const message = input.value.trim();

    if (!message || !currentSession) return;

    // Ensure WebSocket is connected
    if (!websocket || websocket.readyState !== WebSocket.OPEN) {
        await connectWebSocket();
        // Wait a bit for connection to establish
        await new Promise(resolve => setTimeout(resolve, 500));
    }

    if (!websocket || websocket.readyState !== WebSocket.OPEN) {
        addMessage('system', 'Error: WebSocket not connected. Please try again.');
        return;
    }

    // Add user message to chat
    addMessage('user', message);
    input.value = '';

    // Disable input while processing
    input.disabled = true;
    document.getElementById('sendBtn').disabled = true;

    try {
        // Send message via WebSocket
        const messageData = {
            type: 'chat.message',
            content: message
        };

        websocket.send(JSON.stringify(messageData));

    } catch (error) {
        console.error('Failed to send message:', error);
        addMessage('system', 'Error: Failed to send message. Please try again.');
        utils.showAlert('Failed to send message: ' + error.message, 'danger');
    } finally {
        // Re-enable input
        input.disabled = false;
        document.getElementById('sendBtn').disabled = false;
        input.focus();
    }
}

// Add message to chat
function addMessage(type, content, metadata = null) {
    const container = document.getElementById('chatContainer');

    // Remove welcome message if it exists
    const welcomeMessage = container.querySelector('.text-center.text-muted');
    if (welcomeMessage) {
        welcomeMessage.remove();
    }

    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    
    // Parse markdown for assistant messages, escape HTML for others
    let messageContent;
    if (type === 'assistant') {
        messageContent = `<div class="message-content">${marked.parse(content)}</div>`;
    } else {
        messageContent = `<div class="message-content">${utils.escapeHtml(content)}</div>`;
    }
    
    // Add tool call information if present
    if (metadata && metadata.tool_calls) {
        messageContent += '<div class="tool-call-info"><strong>Tool Calls:</strong><br>';
        metadata.tool_calls.forEach(toolCall => {
            messageContent += `🔧 ${toolCall.function.name}(${toolCall.function.arguments})<br>`;
        });
        messageContent += '</div>';
    }
    
    messageDiv.innerHTML = `
        ${messageContent}
        <div class="message-time">${new Date().toLocaleTimeString()}</div>
    `;

    container.appendChild(messageDiv);
    container.scrollTop = container.scrollHeight;
}

// Add tool call start message (shows execution in progress)
function addToolCallStartMessage(toolName, arguments_str) {
    const container = document.getElementById('chatContainer');
    
    const toolDiv = document.createElement('div');
    toolDiv.className = 'message tool';
    toolDiv.id = `tool-${toolName}-${Date.now()}`; // Unique ID for updating
    
    const uniqueId = `tool-content-${Date.now()}`;
    
    toolDiv.innerHTML = `
        <div class=\"tool-indicator\">
            <i class=\"fas fa-cog fa-spin me-1\"></i>Executing Tool: ${utils.escapeHtml(toolName)}
        </div>
        <div class=\"tool-call-info\">
            <div class=\"tool-call-header\" onclick=\"toggleToolCall('${uniqueId}')\">
                <strong>Tool Details</strong>
                <i class=\"fas fa-chevron-down collapse-icon collapsed\" id=\"icon-${uniqueId}\"></i>
            </div>
            <div class=\"tool-call-content collapsed\" id=\"${uniqueId}\">
                <strong>Arguments:</strong><br>
                <code>${utils.escapeHtml(arguments_str || '{}')}</code><br><br>
                <div class=\"text-info\">
                    <i class=\"fas fa-spinner fa-spin me-1\"></i>Executing...
                </div>
            </div>
        </div>
        <div class=\"message-time\">${new Date().toLocaleTimeString()}</div>
    `;
    
    container.appendChild(toolDiv);
    container.scrollTop = container.scrollHeight;
    
    return toolDiv.id;
}

// Update tool call with result
function updateToolCallResult(toolName, result) {
    // Find the most recent tool call div for this tool
    const toolDivs = document.querySelectorAll('.message.tool');
    let targetDiv = null;
    
    for (let i = toolDivs.length - 1; i >= 0; i--) {
        const div = toolDivs[i];
        if (div.innerHTML.includes(`Executing Tool: ${utils.escapeHtml(toolName)}`)) {
            targetDiv = div;
            break;
        }
    }
    
    if (targetDiv) {
        const resultText = typeof result === 'object' ? JSON.stringify(result, null, 2) : result;
        
        // Update the tool indicator to show completion
        const indicator = targetDiv.querySelector('.tool-indicator');
        if (indicator) {
            indicator.innerHTML = `
                <i class=\"fas fa-check-circle text-success me-1\"></i>Tool Completed: ${utils.escapeHtml(toolName)}
            `;
        }
        
        // Update the result section
        const contentDiv = targetDiv.querySelector('.tool-call-content');
        if (contentDiv) {
            const args = contentDiv.querySelector('code').textContent;
            const uniqueId = contentDiv.id;
            contentDiv.innerHTML = `
                <strong>Arguments:</strong><br>
                <code>${utils.escapeHtml(args)}</code><br><br>
                <strong>Result:</strong><br>
                <pre class=\"text-success\">${utils.escapeHtml(resultText)}</pre>
            `;
        }
    }
    
    const container = document.getElementById('chatContainer');
    container.scrollTop = container.scrollHeight;
}

// Update tool call with error
function updateToolCallError(toolName, error) {
    // Find the most recent tool call div for this tool
    const toolDivs = document.querySelectorAll('.message.tool');
    let targetDiv = null;
    
    for (let i = toolDivs.length - 1; i >= 0; i--) {
        const div = toolDivs[i];
        if (div.innerHTML.includes(`Executing Tool: ${utils.escapeHtml(toolName)}`)) {
            targetDiv = div;
            break;
        }
    }
    
    if (targetDiv) {
        // Update the tool indicator to show error
        const indicator = targetDiv.querySelector('.tool-indicator');
        if (indicator) {
            indicator.innerHTML = `
                <i class=\"fas fa-exclamation-triangle text-danger me-1\"></i>Tool Error: ${utils.escapeHtml(toolName)}
            `;
        }
        
        // Update the result section
        const contentDiv = targetDiv.querySelector('.tool-call-content');
        if (contentDiv) {
            const args = contentDiv.querySelector('code').textContent;
            contentDiv.innerHTML = `
                <strong>Arguments:</strong><br>
                <code>${utils.escapeHtml(args)}</code><br><br>
                <strong>Error:</strong><br>
                <pre class=\"text-danger\">${utils.escapeHtml(error)}</pre>
            `;
        }
    }
    
    const container = document.getElementById('chatContainer');
    container.scrollTop = container.scrollHeight;
}

// Add tool call message to chat
function addToolCallMessage(toolName, arguments_str, result) {
    const container = document.getElementById('chatContainer');
    
    const toolDiv = document.createElement('div');
    toolDiv.className = 'message tool';
    
    const resultText = typeof result === 'object' ? JSON.stringify(result, null, 2) : result;
    const uniqueId = `tool-content-${Date.now()}`;
    
    toolDiv.innerHTML = `
        <div class="tool-indicator">
            <i class="fas fa-check-circle text-success me-1"></i>Tool Call: ${utils.escapeHtml(toolName)}
        </div>
        <div class="tool-call-info">
            <div class="tool-call-header" onclick="toggleToolCall('${uniqueId}')">
                <strong>Tool Details</strong>
                <i class="fas fa-chevron-down collapse-icon collapsed" id="icon-${uniqueId}"></i>
            </div>
            <div class="tool-call-content collapsed" id="${uniqueId}">
                <strong>Arguments:</strong><br>
                <code>${utils.escapeHtml(arguments_str || '{}')}</code><br><br>
                <strong>Result:</strong><br>
                <pre class="text-success">${utils.escapeHtml(resultText)}</pre>
            </div>
        </div>
        <div class="message-time">${new Date().toLocaleTimeString()}</div>
    `;
    
    container.appendChild(toolDiv);
    container.scrollTop = container.scrollHeight;
}

// Toggle tool call content visibility
function toggleToolCall(contentId) {
    const content = document.getElementById(contentId);
    const icon = document.getElementById(`icon-${contentId}`);
    
    if (content && icon) {
        content.classList.toggle('collapsed');
        icon.classList.toggle('collapsed');
    }
}

// Add streaming message placeholder
function addStreamingMessage(type, content) {
    const container = document.getElementById('chatContainer');

    // Remove welcome message if it exists
    const welcomeMessage = container.querySelector('.text-center.text-muted');
    if (welcomeMessage) {
        welcomeMessage.remove();
    }

    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type} streaming`;
    
    // Parse markdown for assistant messages, escape HTML for others
    let messageContent;
    if (type === 'assistant') {
        messageContent = `<div class="message-content">${marked.parse(content)}<span class="typing-indicator">▋</span></div>`;
    } else {
        messageContent = `<div class="message-content">${utils.escapeHtml(content)}<span class="typing-indicator">▋</span></div>`;
    }
    
    messageDiv.innerHTML = `
        ${messageContent}
        <div class="message-time">${new Date().toLocaleTimeString()}</div>
    `;

    container.appendChild(messageDiv);
    container.scrollTop = container.scrollHeight;

    // Store reference for streaming updates
    if (currentStreamingMessage) {
        currentStreamingMessage.element = messageDiv;
    }
}

// Update streaming message content
function updateStreamingMessage(content) {
    if (currentStreamingMessage && currentStreamingMessage.element) {
        const contentDiv = currentStreamingMessage.element.querySelector('.message-content');
        if (contentDiv) {
            // Parse markdown for assistant messages
            const messageType = currentStreamingMessage.element.classList.contains('assistant') ? 'assistant' : 'user';
            if (messageType === 'assistant') {
                contentDiv.innerHTML = `${marked.parse(content)}<span class="typing-indicator">▋</span>`;
            } else {
                contentDiv.innerHTML = `${utils.escapeHtml(content)}<span class="typing-indicator">▋</span>`;
            }
            // Auto-scroll to bottom
            const container = document.getElementById('chatContainer');
            container.scrollTop = container.scrollHeight;
        }
    }
}

// Finalize streaming message
function finalizeStreamingMessage() {
    if (currentStreamingMessage && currentStreamingMessage.element) {
        const contentDiv = currentStreamingMessage.element.querySelector('.message-content');
        if (contentDiv) {
            // Parse markdown for assistant messages, escape HTML for others
            const messageType = currentStreamingMessage.element.classList.contains('assistant') ? 'assistant' : 'user';
            if (messageType === 'assistant') {
                contentDiv.innerHTML = marked.parse(currentStreamingMessage.content);
            } else {
                contentDiv.innerHTML = utils.escapeHtml(currentStreamingMessage.content);
            }
            currentStreamingMessage.element.classList.remove('streaming');
        }
    }
}

// Clear chat messages
function clearChatMessages() {
    const container = document.getElementById('chatContainer');
    container.innerHTML = '';
}

// Clear chat and reset session
function clearChat() {
    const confirmed = confirm('Are you sure you want to clear the chat history?');
    if (confirmed) {
        clearChatMessages();
        messageCount = 0;
        document.getElementById('messageCount').textContent = '0';

        // Close WebSocket connection
        if (websocket) {
            websocket.close();
            websocket = null;
        }

        // Reset session
        currentSession = null;
        currentStreamingMessage = null;

        // Update UI
        document.getElementById('sessionCard').style.display = 'none';
        document.getElementById('messageInput').disabled = true;
        document.getElementById('sendBtn').disabled = true;
        document.getElementById('toolCallIndicator').style.display = 'none';

        utils.showAlert('Chat cleared', 'info', 2000);
    }
}

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (websocket) {
        websocket.close();
    }
});

</script>
{% endblock %}
