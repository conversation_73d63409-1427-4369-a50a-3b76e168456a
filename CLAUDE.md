# Xapa AI - Claude Memory Documentation

## Project Overview
**Xapa AI** is a comprehensive multi-user, multi-session, multi-role AI agent system built with FastAPI. It provides a robust platform for AI conversations with persistent sessions, role-based AI agents, and real-time communication capabilities.

## Key Architecture

### Core Components
- **Backend**: Python 3.11+ with FastAPI
- **Database**: PostgreSQL for persistent storage
- **Cache**: Redis for session context and performance
- **AI Models**: OpenAI GPT-4, Azure OpenAI (with provider abstraction)
- **Authentication**: JWT-based with OAuth2 and Auth0 integration
- **Deployment**: Docker & Docker Compose ready

### Directory Structure
```
/mnt/d/xapa/xapa_ai/
├── app/
│   ├── main.py                 # FastAPI application entry point
│   ├── api/                    # API endpoints (auth, chat, session, roles, tools, websocket)
│   ├── agent/                  # AI agent system (factory, roles, tools)
│   ├── core/                   # Core functionality (config, security, dependencies, exceptions)
│   ├── embedding/              # Embedding provider abstraction
│   ├── llm/                    # LLM provider abstraction
│   ├── models/                 # SQLAlchemy ORM models
│   ├── schemas/                # Pydantic validation schemas
│   ├── services/               # Business logic services
│   ├── session/                # Session management (Redis storage)
│   └── utils/                  # Utilities
├── config/                     # Nginx configuration
├── static/                     # Web assets (CSS, JS)
├── templates/                  # Jinja2 HTML templates
├── roles/                      # Role configuration files
├── tests/                      # Test files
└── docs/                       # Design documentation
```

## Key Features

### 1. Authentication System
- **Anonymous Access**: Temporary JWT tokens for quick usage
- **External JWT**: Accepts tokens from other apps with same secret
- **Auth0 Integration**: OAuth2 flow for web interface
- **User Management**: Auto-creation from tokens, active/inactive status

### 2. Session Management
- **Multi-Session**: Users can create multiple named sessions
- **Persistence**: Redis caching + PostgreSQL storage
- **Context Retention**: Conversation history maintained across sessions
- **Limits**: Configurable max sessions per user (default: 10)

### 3. Role-Based AI Agents
- **Dynamic Loading**: Roles loaded from database and YAML files
- **Tool Integration**: Each role has specific tools and capabilities
- **Memory Management**: Conversation history with configurable limits
- **Streaming Support**: Real-time response generation

### 4. Multi-Provider LLM Support
- **OpenAI**: GPT-4 integration
- **Azure OpenAI**: Enterprise-grade deployment
- **Provider Abstraction**: Easy switching between providers

### 5. Embedding Support
- **Text Vectorization**: Multiple embedding providers
- **Similarity Search**: Semantic search capabilities
- **Clustering**: K-means clustering for text grouping

## Database Models

### Core Models
- **User**: User accounts (id, username, email, password_hash, is_active)
- **Session**: Chat sessions (id, user_id, session_key, name, role_name, context, is_active)
- **Message**: Chat messages (id, session_id, role, content, message_metadata, sequence_number)
- **Role**: AI roles (id, name, display_name, description, system_prompt, tools, config, is_active)

### Key Relationships
- Users → Sessions (one-to-many)
- Sessions → Messages (one-to-many)
- Sessions → Roles (many-to-one)

## API Endpoints

### Authentication
- `POST /api/v1/auth/anonymous` - Get anonymous token
- `GET /api/v1/auth/verify` - Verify external JWT token

### Session Management
- `GET /api/v1/sessions/` - List user sessions
- `POST /api/v1/sessions/` - Create new session
- `PUT /api/v1/sessions/{session_key}` - Update session
- `DELETE /api/v1/sessions/{session_key}` - Delete session

### Chat API
- `POST /api/v1/chat/` - Send message (REST)
- `GET /api/v1/chat/{session_key}/history` - Get message history
- `DELETE /api/v1/chat/{session_key}/history` - Clear history
- `WS /ws/chat?session_key={key}&token={jwt}` - WebSocket streaming

### Role Management
- `GET /api/v1/roles/` - List available roles
- `POST /api/v1/roles/` - Create new role
- `PUT /api/v1/roles/{role_name}` - Update role
- `DELETE /api/v1/roles/{role_name}` - Delete role

## Configuration

### Environment Variables
- `DATABASE_URL`: PostgreSQL connection string
- `REDIS_URL`: Redis connection string
- `LLM_PROVIDER`: Provider choice (openai, azure_openai)
- `SECRET_KEY`: JWT signing secret
- `MAX_SESSIONS_PER_USER`: Session limit per user
- Provider-specific keys (OPENAI_API_KEY, AZURE_OPENAI_API_KEY, etc.)

### Key Settings
- Default embedding provider: Azure OpenAI
- JWT expiration: 24 hours (configurable)
- Anonymous token expiration: 1 hour
- Max messages per session: 1000

## Security Features

### Authentication
- JWT tokens with HS256 signing
- Anonymous user identification by username pattern (`anonymous_*`)
- Auth0 domain restrictions for web interface
- Automatic user creation from valid tokens

### Authorization
- Session-scoped access (users can only access their own sessions)
- Role-based tool access
- Token validation middleware

## Recent Modifications

### User Info Attachment (Latest)
- **Location**: `app/api/chat.py:109-115`
- **Feature**: Skip attaching user info to messages when user is anonymous
- **Logic**: Checks if username starts with `anonymous_` to determine if user is anonymous
- **Implementation**: Only authenticated users get user metadata in message metadata

## Development Commands

### Running the Application
```bash
# Using Docker (recommended)
docker-compose up -d

# Local development
pip install -r requirements.txt
uvicorn app.main:app --reload
```

### Testing
```bash
# Run comprehensive API tests
python tests/test_api.py

# Run specific test suites
python tests/test_auth.py
python tests/test_websocket.py
```

### Database Operations
```bash
# Database migrations (if using Alembic)
alembic upgrade head

# Create new migration
alembic revision --autogenerate -m "description"
```

## Common Patterns

### Error Handling
- Custom exceptions: `AuthenticationError`, `ValidationError`, `LLMError`, `SessionError`
- Consistent error responses with appropriate HTTP status codes
- Database rollback on errors

### Dependency Injection
- `get_current_user()`: Requires valid JWT token
- `get_anonymous_user()`: Allows anonymous access
- `get_db()`: Database session dependency

### Caching Strategy
- Redis for session context and recent messages
- Database for persistent storage
- LRU cache for configuration settings

## Tools and Integrations

### Available Tools
- Math solver and calculator
- Knowledge search and retrieval
- Code execution and analysis
- Health monitoring
- User database lookup
- Pinecone vector search
- Writing assistance

### External Services
- Auth0 for OAuth2 authentication
- Pinecone for vector search
- Multiple LLM providers
- Redis for caching
- PostgreSQL for persistence

This documentation serves as a comprehensive reference for understanding the Xapa AI system architecture, features, and implementation details.