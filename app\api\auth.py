from fastapi import APIRouter, Depends, Request, HTTPException, status
from fastapi.responses import RedirectResponse, HTMLResponse
from fastapi.templating import Jinja2Templates
from sqlalchemy.orm import Session

from app.models.base import get_db
from app.models.user import User
from app.schemas.auth import UserResponse
from app.core.dependencies import get_current_user, get_current_web_user
from app.core.exceptions import AuthenticationError
from app.services.auth0_service import auth0_service

router = APIRouter()
templates = Jinja2Templates(directory="templates")


@router.get("/verify", response_model=UserResponse)
async def verify_token_and_get_user(
    current_user: User = Depends(get_current_user)
):
    """
    Verify JWT token generated by other applications with the same secret key
    and return user information.

    This endpoint accepts JWT tokens that were generated by external applications
    using the same secret key. If the user doesn't exist in the database,
    a new user will be automatically created based on the token payload.

    Args:
        current_user: The authenticated user, injected by dependency.

    Returns:
        UserResponse: User information corresponding to the token.

    Raises:
        AuthenticationError: If token is invalid or expired
    """
    return current_user





# Web Authentication Routes (Auth0)

@router.get("/login", response_class=HTMLResponse)
async def login_page(
    request: Request,
    current_user: User = Depends(get_current_web_user)
):
    """
    Display login page or redirect to Auth0 if configured.
    """
    if not auth0_service:
        # Auth0 not configured, show basic login page
        return templates.TemplateResponse("auth/login.html", {
            "request": request,
            "auth0_configured": False,
            "error": "Auth0 authentication is not configured"
        })

    # Check if user is already authenticated)
    if current_user:
        return RedirectResponse(url="/", status_code=302)

    return templates.TemplateResponse("auth/login.html", {
        "request": request,
        "auth0_configured": True
    })


@router.get("/login/auth0")
async def auth0_login(request: Request):
    """
    Initiate Auth0 login flow.
    """
    if not auth0_service:
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="Auth0 authentication is not configured"
        )

    # Generate authorization URL
    auth_url, state = auth0_service.get_authorization_url()

    # Store state in session for CSRF protection
    request.session['auth0_state'] = state

    return RedirectResponse(url=auth_url, status_code=302)


@router.get("/callback")
async def auth0_callback(
    request: Request,
    code: str = None,
    state: str = None,
    error: str = None,
    db: Session = Depends(get_db)
):
    """
    Handle Auth0 callback after authentication.
    """
    if not auth0_service:
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="Auth0 authentication is not configured"
        )

    # Handle authentication errors
    if error:
        return templates.TemplateResponse("auth/login.html", {
            "request": request,
            "auth0_configured": True,
            "error": f"Authentication failed: {error}"
        })

    # Validate state parameter for CSRF protection
    session_state = request.session.get('auth0_state')
    if not state or state != session_state:
        return templates.TemplateResponse("auth/login.html", {
            "request": request,
            "auth0_configured": True,
            "error": "Invalid state parameter. Please try again."
        })

    # Clear state from session
    request.session.pop('auth0_state', None)

    if not code:
        return templates.TemplateResponse("auth/login.html", {
            "request": request,
            "auth0_configured": True,
            "error": "No authorization code received"
        })

    try:
        # Exchange code for tokens
        tokens = auth0_service.exchange_code_for_tokens(code, state)

        # Get user info (this will check domain restrictions)
        user_info = auth0_service.get_user_info(tokens['access_token'])

        # Validate ID token
        auth0_service.validate_id_token(tokens['id_token'])

        # Store user info in session
        request.session['user'] = user_info
        request.session['tokens'] = tokens

        # Redirect to home page
        return RedirectResponse(url="/", status_code=302)

    except AuthenticationError as e:
        # Handle domain restriction and other auth errors
        return templates.TemplateResponse("auth/login.html", {
            "request": request,
            "auth0_configured": True,
            "error": str(e)
        })
    except Exception as e:
        # Handle other unexpected errors
        return templates.TemplateResponse("auth/login.html", {
            "request": request,
            "auth0_configured": True,
            "error": f"Authentication failed: {str(e)}"
        })


@router.get("/logout")
async def logout(request: Request):
    """
    Logout user and redirect to Auth0 logout.
    """
    # Clear session
    request.session.clear()

    return RedirectResponse(url="/", status_code=302)


@router.get("/profile", response_class=HTMLResponse)
async def profile_page(
    request: Request, 
    current_user: User = Depends(get_current_web_user)
):
    """
    Display user profile page.
    """
    if not current_user:
        return RedirectResponse(url="/auth/login", status_code=302)

    # Get additional user info from session if available
    user_info = request.session.get('user', {})

    return templates.TemplateResponse("auth/profile.html", {
        "request": request,
        "user": current_user,
        "user_info": user_info
    })