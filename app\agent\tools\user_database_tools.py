"""
User database tools for retrieving user information from external PostgreSQL database.

This module contains tools for querying user data from an external PostgreSQL database
containing user profiles with first_name, last_name, preferred_name, title, and bio.
"""

import logging
import asyncio
import json
from typing import Dict, Any, List, Optional
import asyncpg
from contextlib import asynccontextmanager

from app.agent.tools.base import BaseTool, ToolSchema, ToolResult, tool_registry
from app.core.config import settings

logger = logging.getLogger(__name__)


class UserDatabaseTool(BaseTool):
    """Tool for retrieving user information from external PostgreSQL database."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(config)
        self._connection_pool = None
        self.user_table_name = self.config.get("user_table_name", "user")
        self.user_style_table_name = self.config.get("user_style_table_name", "user_style")
        self.user_style_mapping_table_name = self.config.get("user_style_mapping_table_name", "user_style_mapping")

    async def get_pool(self):
        """Get or create a database connection pool."""
        if self._connection_pool is None:
            if not settings.external_user_db_url:
                raise ValueError("External user database URL not configured. Please set EXTERNAL_USER_DB_URL environment variable.")
            try:
                self._connection_pool = await asyncpg.create_pool(settings.external_user_db_url)
            except Exception as e:
                logger.error(f"Failed to create database connection pool: {str(e)}")
                raise
        return self._connection_pool

    @asynccontextmanager
    async def get_connection(self):
        """Get a database connection from the pool."""
        pool = await self.get_pool()
        connection = None
        try:
            connection = await pool.acquire()
            yield connection
        finally:
            if connection:
                await pool.release(connection)

    @property
    def name(self) -> str:
        return "user_database_lookup"

    @property
    def description(self) -> str:
        return "Retrieve user information from external PostgreSQL database including name, title, and bio"

    def get_schema(self) -> ToolSchema:
        return ToolSchema(
            name=self.name,
            description=self.description,
            parameters={
                "type": "object",
                "properties": {
                    "search_value": {
                        "type": "string",
                        "description": "Value to search for (name, email, title, or 'me' for current user)."
                    },
                    "limit": {
                        "type": "integer",
                        "description": "Maximum number of results to return",
                        "default": 1,
                        "minimum": 1,
                        "maximum": 100
                    }
                },
                "required": ["search_value"]
            }
        )

    async def execute(self, user_context: Optional[Dict[str, Any]] = None, **kwargs) -> ToolResult:
        """
        Retrieve user information from external database.
        
        Args:
            user_context: User context containing user_id, tenant_id, session_key
            search_value: Value to search for (use "me" to search for current user)
            limit: Maximum number of results
            
        Returns:
            ToolResult with user information
        """
        try:
            search_value = kwargs.get("search_value", "").strip()
            limit = kwargs.get("limit", 1)

            if not search_value:
                return ToolResult(
                    success=False,
                    error="search_value is required"
                )

            # Handle "me" search by looking up current user ID
            if search_value.lower() == "me":
                if not user_context or not user_context.get("user_id"):
                    return ToolResult(
                        success=False,
                        error="Cannot search for 'me' without valid user context"
                    )
                
                user_id = user_context.get("user_id")
                query = f"""
                    SELECT 
                        u.id,
                        u.first_name,
                        u.last_name,
                        u.preferred_name,
                        u.title,
                        u.email,
                        u.bio,
                        COALESCE(u.preferred_name, CONCAT(u.first_name, ' ', u.last_name)) as display_name,
                        CASE 
                            WHEN COUNT(us.id) > 0 THEN 
                                ARRAY_AGG(
                                    JSON_BUILD_OBJECT(usm.style_name, us.style_value)
                                ) FILTER (WHERE usm.style_name IS NOT NULL AND us.style_value IS NOT NULL)
                            ELSE NULL
                        END as styles
                    FROM "{self.user_table_name}" u
                    LEFT JOIN {self.user_style_table_name} us ON u.id = us.user_id AND us.is_deleted = false AND us.status = 'active'
                    LEFT JOIN {self.user_style_mapping_table_name} usm ON us.style_id = usm.id AND usm.is_deleted = false AND usm.status = 'active'
                    WHERE u.id = $1
                    GROUP BY u.id, u.first_name, u.last_name, u.preferred_name, u.title, u.email, u.bio
                """
                
                async with self.get_connection() as conn:
                    rows = await conn.fetch(query, user_id)
                    users = []
                    for row in rows:
                        user = dict(row)
                        # Parse JSON strings in styles array to actual JSON objects
                        if user.get('styles'):
                            user['styles'] = [json.loads(style) if isinstance(style, str) else style for style in user['styles']]
                        users.append(user)
                    
                    return ToolResult(
                        success=True,
                        result={
                            "search_value": search_value,
                            "users": users,
                            "total_found": len(users),
                            "limit_applied": limit
                        },
                        metadata={
                            "operation": "user_database_lookup",
                            "result_count": len(users),
                            "search_type": "user_id"
                        }
                    )
            
            # Use a tsquery for full-text search with proper escaping
            # We'll search for the value as a whole phrase and also with terms OR'd together
            # Escape special characters and use parameterized queries for safety
            search_terms = ' & '.join(search_value.split())
            
            query = f"""
                SELECT 
                    u.id,
                    u.first_name,
                    u.last_name,
                    u.preferred_name,
                    u.title,
                    u.email,
                    u.bio,
                    COALESCE(u.preferred_name, CONCAT(u.first_name, ' ', u.last_name)) as display_name,
                    ts_rank(
                        to_tsvector('english', COALESCE(u.first_name, '') || ' ' || COALESCE(u.last_name, '') || ' ' || COALESCE(u.preferred_name, '') || ' ' || COALESCE(u.title, '') || ' ' || COALESCE(u.email, '')),
                        to_tsquery('english', $2)
                    ) as rank,
                    CASE 
                        WHEN COUNT(us.id) > 0 THEN 
                            ARRAY_AGG(
                                JSON_BUILD_OBJECT(usm.style_name, us.style_value)
                            ) FILTER (WHERE usm.style_name IS NOT NULL AND us.style_value IS NOT NULL)
                        ELSE NULL
                    END as styles
                FROM "{self.user_table_name}" u
                LEFT JOIN {self.user_style_table_name} us ON u.id = us.user_id AND us.is_deleted = false AND us.status = 'active'
                LEFT JOIN {self.user_style_mapping_table_name} usm ON us.style_id = usm.id AND usm.is_deleted = false AND usm.status = 'active'
                WHERE to_tsvector('english', COALESCE(u.first_name, '') || ' ' || COALESCE(u.last_name, '') || ' ' || COALESCE(u.preferred_name, '') || ' ' || COALESCE(u.title, '') || ' ' || COALESCE(u.email, '')) @@ to_tsquery('english', $2)
                GROUP BY u.id, u.first_name, u.last_name, u.preferred_name, u.title, u.email, u.bio
                ORDER BY rank DESC
                LIMIT $1
            """

            async with self.get_connection() as conn:
                rows = await conn.fetch(query, limit, search_terms)
                users = []
                for row in rows:
                    user = dict(row)
                    # Parse JSON strings in styles array to actual JSON objects
                    if user.get('styles'):
                        user['styles'] = [json.loads(style) if isinstance(style, str) else style for style in user['styles']]
                    users.append(user)
                
                return ToolResult(
                    success=True,
                    result={
                        "search_value": search_value,
                        "users": users,
                        "total_found": len(users),
                        "limit_applied": limit
                    },
                    metadata={
                        "operation": "user_database_lookup",
                        "result_count": len(users),
                        "search_type": "full_text"
                    }
                )

        except ValueError as e:
            logger.error(f"User database lookup validation error: {str(e)}")
            return ToolResult(success=False, error=str(e))
        except Exception as e:
            logger.error(f"User database lookup error: {str(e)}")
            return ToolResult(success=False, error=f"Database lookup failed: {str(e)}")


# Register tools
tool_registry.register_tool(UserDatabaseTool())
