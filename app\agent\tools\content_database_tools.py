"""
Xperience tools for retrieving experience and quest data from external database.

This module contains tools for querying xperience and quest data from an external PostgreSQL database
containing experiences, quests, and their associations.
"""

import logging
import asyncio
import json
from typing import Dict, Any, List, Optional
import asyncpg
from contextlib import asynccontextmanager

from app.agent.tools.base import <PERSON>Tool, ToolSchema, ToolResult, tool_registry
from app.core.config import settings

logger = logging.getLogger(__name__)


class ContentDatabaseTool(BaseTool):
    """Tool for retrieving xperience and quest information from external PostgreSQL database."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(config)
        self._connection_pool = None
        self.xperience_table_name = self.config.get("xperience_table_name", "xperience")
        self.quest_table_name = self.config.get("quest_table_name", "quest")
        self.association_table_name = self.config.get("association_table_name", "xperience_quest_association")

    async def get_pool(self):
        """Get or create a database connection pool."""
        if self._connection_pool is None:
            if not settings.external_user_db_url:
                raise ValueError("External database URL not configured. Please set EXTERNAL_USER_DB_URL environment variable.")
            try:
                self._connection_pool = await asyncpg.create_pool(settings.external_user_db_url)
            except Exception as e:
                logger.error(f"Failed to create database connection pool: {str(e)}")
                raise
        return self._connection_pool

    @asynccontextmanager
    async def get_connection(self):
        """Get a database connection from the pool."""
        pool = await self.get_pool()
        connection = None
        try:
            connection = await pool.acquire()
            yield connection
        finally:
            if connection:
                await pool.release(connection)

    @property
    def name(self) -> str:
        return "content_database_lookup"

    @property
    def description(self) -> str:
        return "Retrieve xperience and quest information from the database including their associations"

    def get_schema(self) -> ToolSchema:
        return ToolSchema(
            name=self.name,
            description=self.description,
            parameters={
                "type": "object",
                "properties": {
                    "search_type": {
                        "type": "string",
                        "enum": ["xperience", "quest", "both"],
                        "description": "Type of data to retrieve",
                        "default": "both"
                    },
                    "search_value": {
                        "type": "string",
                        "description": "Value to search for in name or description (optional for listing all)"
                    },
                    "limit": {
                        "type": "integer",
                        "description": "Maximum number of results to return (optional, no limit if not specified)",
                        "minimum": 1
                    }
                },
                "required": []
            }
        )

    async def execute(self, user_context: Optional[Dict[str, Any]] = None, **kwargs) -> ToolResult:
        """
        Retrieve xperience and quest information from external database.
        
        Args:
            user_context: User context containing user_id, tenant_id, session_key
            search_type: Type of data to retrieve ('xperience', 'quest', 'both')
            search_value: Value to search for (optional)
            limit: Maximum number of results (optional)
            
        Returns:
            ToolResult with xperience/quest information
        """
        try:
            search_type = kwargs.get("search_type", "both")
            search_value = kwargs.get("search_value", "").strip()
            limit = kwargs.get("limit")

            results = {}

            async with self.get_connection() as conn:
                if search_type in ["xperience", "both"]:
                    # Get xperiences without JSON aggregation first
                    where_conditions = ["is_deleted = false"]
                    params = []
                    param_count = 0
                    
                    if search_value:
                        param_count += 1
                        where_conditions.append(f"(name ILIKE ${param_count} OR description ILIKE ${param_count})")
                        params.append(f"%{search_value}%")
                    
                    limit_clause = ""
                    if limit is not None:
                        param_count += 1
                        params.append(limit)
                        limit_clause = f"LIMIT ${param_count}"
                    
                    xperience_query = f"""
                        SELECT 
                            id, name, description, level, learning_objective,
                            status, is_active, is_deleted,
                            requires_quest_completion, keys_required, level_required
                        FROM {self.xperience_table_name}
                        WHERE {' AND '.join(where_conditions)}
                        ORDER BY date_created DESC
                        {limit_clause}
                    """
                    
                    xperience_rows = await conn.fetch(xperience_query, *params)
                    xperience_list = []
                    
                    for row in xperience_rows:
                        xp_dict = dict(row)
                        # Convert UUID to string and handle dates
                        xp_dict["id"] = str(xp_dict["id"])
                        
                        # Get associated quests separately
                        quest_assoc_query = f"""
                            SELECT q.id, q.name, xqa."order"
                            FROM {self.association_table_name} xqa
                            JOIN {self.quest_table_name} q ON xqa.quest_id = q.id
                            WHERE xqa.xperience_id = $1 AND q.is_deleted = false
                            ORDER BY xqa."order"
                        """
                        quest_rows = await conn.fetch(quest_assoc_query, row["id"])
                        associated_quests = []
                        for quest_row in quest_rows:
                            associated_quests.append({
                                "quest_id": str(quest_row["id"]),
                                "quest_name": quest_row["name"],
                                "order": quest_row["order"]
                            })
                        
                        xp_dict["associated_quests"] = associated_quests
                        xperience_list.append(xp_dict)
                    
                    results["xperiences"] = xperience_list

                if search_type in ["quest", "both"]:
                    # Get quests without JSON aggregation first
                    where_conditions = ["is_deleted = false"]
                    params = []
                    param_count = 0
                    
                    if search_value:
                        param_count += 1
                        where_conditions.append(f"(name ILIKE ${param_count} OR description ILIKE ${param_count})")
                        params.append(f"%{search_value}%")
                    
                    limit_clause = ""
                    if limit is not None:
                        param_count += 1
                        params.append(limit)
                        limit_clause = f"LIMIT ${param_count}"
                    
                    quest_query = f"""
                        SELECT 
                            id, name, description, level, learning_objective,
                            status, is_active, is_deleted,
                            configs, "index"
                        FROM {self.quest_table_name}
                        WHERE {' AND '.join(where_conditions)}
                        ORDER BY date_created DESC
                        {limit_clause}
                    """
                    
                    quest_rows = await conn.fetch(quest_query, *params)
                    quest_list = []
                    
                    for row in quest_rows:
                        quest_dict = dict(row)
                        # Convert UUID to string and handle dates
                        quest_dict["id"] = str(quest_dict["id"])
                        
                        # Get associated xperiences separately
                        xp_assoc_query = f"""
                            SELECT x.id, x.name, xqa."order"
                            FROM {self.association_table_name} xqa
                            JOIN {self.xperience_table_name} x ON xqa.xperience_id = x.id
                            WHERE xqa.quest_id = $1 AND x.is_deleted = false
                            ORDER BY xqa."order"
                        """
                        xp_rows = await conn.fetch(xp_assoc_query, row["id"])
                        associated_xperiences = []
                        for xp_row in xp_rows:
                            associated_xperiences.append({
                                "xperience_id": str(xp_row["id"]),
                                "xperience_name": xp_row["name"],
                                "order": xp_row["order"]
                            })
                        
                        quest_dict["associated_xperiences"] = associated_xperiences
                        quest_list.append(quest_dict)
                    
                    results["quests"] = quest_list

            return ToolResult(
                success=True,
                result=results,
                metadata={
                    "operation": "content_database_lookup",
                    "search_type": search_type,
                    "search_value": search_value,
                    "limit_applied": limit,
                    "result_counts": {
                        "xperiences": len(results.get("xperiences", [])),
                        "quests": len(results.get("quests", []))
                    }
                }
            )

        except ValueError as e:
            logger.error(f"Content database lookup validation error: {str(e)}")
            return ToolResult(success=False, error=str(e))
        except Exception as e:
            logger.error(f"Content database lookup error: {str(e)}")
            return ToolResult(success=False, error=f"Database retrieval failed: {str(e)}")


# Register the tool
tool_registry.register_tool(ContentDatabaseTool())